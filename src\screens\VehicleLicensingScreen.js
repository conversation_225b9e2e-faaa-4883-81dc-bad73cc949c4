import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, ScrollView, Alert } from 'react-native';
import CustomButton from '../components/CustomButton';
import AppHeader from '../components/AppHeader';
import GradientBackground from '../components/GradientBackground';

const VehicleLicensingScreen = ({ navigation }) => {
  const [vin, setVin] = useState('');
  const [licensePlate, setLicensePlate] = useState('');
  const [model, setModel] = useState('');
  const [make, setMake] = useState('');
  const [color, setColor] = useState('');
  const [ownerName, setOwnerName] = useState('');
  const [ownerId, setOwnerId] = useState('');

  const handleSubmit = () => {
    // Basic validation
    if (!vin || !licensePlate || !model || !make || !color || !ownerName || !ownerId) {
      Alert.alert('Missing Information', 'Please fill in all fields.');
      return;
    }

    // Here you would typically handle the submission logic,
    // e.g., sending data to an API or processing it locally.
    console.log('Vehicle Licensing Data:', {
      vin,
      licensePlate,
      model,
      make,
      color,
      ownerName,
      ownerId,
    });

    Alert.alert('Submission Successful', 'Vehicle licensing details submitted.');
    // Optionally navigate back or to a confirmation screen
    // navigation.goBack();
  };

  return (
    <GradientBackground>
      <AppHeader title="Vehicle Licensing" navigation={navigation} />
      <ScrollView contentContainerStyle={styles.container}>
        <Text style={styles.title}>Enter Vehicle and Owner Details</Text>

        <TextInput
          style={styles.input}
          placeholder="VIN (Vehicle Identification Number)"
          value={vin}
          onChangeText={setVin}
          autoCapitalize="characters"
        />
        <TextInput
          style={styles.input}
          placeholder="License Plate Number"
          value={licensePlate}
          onChangeText={setLicensePlate}
          autoCapitalize="characters"
        />
        <TextInput
          style={styles.input}
          placeholder="Vehicle Model"
          value={model}
          onChangeText={setModel}
        />
        <TextInput
          style={styles.input}
          placeholder="Vehicle Make"
          value={make}
          onChangeText={setMake}
        />
        <TextInput
          style={styles.input}
          placeholder="Vehicle Color"
          value={color}
          onChangeText={setColor}
        />
        <TextInput
          style={styles.input}
          placeholder="Owner's Full Name"
          value={ownerName}
          onChangeText={setOwnerName}
        />
        <TextInput
          style={styles.input}
          placeholder="Owner's ID Number (South African)"
          value={ownerId}
          onChangeText={setOwnerId}
          keyboardType="numeric"
        />

        <CustomButton text="Submit Application" onPress={handleSubmit} />
      </ScrollView>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    width: '100%',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
    fontSize: 16,
  },
});

export default VehicleLicensingScreen;