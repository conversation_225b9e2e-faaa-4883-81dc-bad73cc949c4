import 'react-native-get-random-values';
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { COLORS } from './src/theme/colors';

// Import screens
import SplashScreen from './src/screens/SplashScreen';
import LoginScreen from './src/screens/LoginScreen';
import RegisterScreen from './src/screens/RegisterScreen';
import ConfirmationScreen from './src/screens/ConfirmationScreen';
import CallHistoryScreen from './src/screens/CallHistoryScreen';
import ProfileScreen from './src/screens/ProfileScreen';
import UtilitiesScreen from './src/screens/UtilitiesScreen';
import PaymentDetailsScreen from './src/screens/PaymentDetailsScreen';
import PaymentConfirmationScreen from './src/screens/PaymentConfirmationScreen';

// Import health services screens
import HealthServicesScreen from './src/screens/HealthServicesScreen';
import BookingScreen from './src/screens/BookingScreen';
import NotificationsScreen from './src/screens/NotificationsScreen';
import ClinicLocatorScreen from './src/screens/ClinicLocatorScreen';
import PrescriptionRefillScreen from './src/screens/PrescriptionRefillScreen';
import MedicalRecordsScreen from './src/screens/MedicalRecordsScreen';

// Import RDP housing screens
import RDPHousingScreen from './src/screens/RDPHousingScreen';
import RDPApplicationScreen from './src/screens/RDPApplicationScreen';
import RDPStatusScreen from './src/screens/RDPStatusScreen';

// Import doctor admin screens
import DoctorDashboardScreen from './src/screens/admin/DoctorDashboardScreen';
import DoctorAppointmentsScreen from './src/screens/admin/DoctorAppointmentsScreen';
import DoctorPatientDetailsScreen from './src/screens/admin/DoctorPatientDetailsScreen';
import DoctorPrescriptionScreen from './src/screens/admin/DoctorPrescriptionScreen';
import DoctorReportsScreen from './src/screens/admin/DoctorReportsScreen';

// Import admin screens
import AdminDashboardScreen from './src/screens/admin/AdminDashboardScreen';
import AdminCallQueueScreen from './src/screens/admin/AdminCallQueueScreen';
import AdminCallDetailsScreen from './src/screens/admin/AdminCallDetailsScreen';
import AdminIssuesScreen from './src/screens/admin/AdminIssuesScreen';
import AdminIssueDetailsScreen from './src/screens/admin/AdminIssueDetailsScreen';

// Import navigation
import BottomTabNavigator from './src/navigation/BottomTabNavigator';

// Import mock auth context
import { MockAuthProvider, useMockAuth } from './src/context/MockAuthContext';

// Create stack navigator
const Stack = createNativeStackNavigator();

// Main navigation component
const AppNavigator = () => {
  const { currentUser } = useMockAuth();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: COLORS.white },
        // Disable animations completely to avoid react-native-screens issues
        animation: 'none',
        // Add header style to prevent potential theme issues
        headerStyle: {
          backgroundColor: COLORS.white,
        },
        headerTintColor: COLORS.primaryGreen,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        // Ensure all required font properties are set
        headerTitleAllowFontScaling: true,
        headerBackAllowFontScaling: true,
      }}
      initialRouteName="Splash"
    >
      {/* Always include all screens, but conditionally redirect in useEffect */}
      <Stack.Screen
        name="Splash"
        component={SplashScreen}
        options={{
          gestureEnabled: false, // Disable gestures for splash screen
        }}
      />

      {/* Auth screens */}
      <Stack.Group>
        <Stack.Screen name="Login" component={LoginScreen} />
        <Stack.Screen name="Register" component={RegisterScreen} />
        <Stack.Screen name="Confirmation" component={ConfirmationScreen} />
      </Stack.Group>

      {/* Admin screens */}
      <Stack.Group>
        <Stack.Screen name="AdminDashboard" component={AdminDashboardScreen} />
        <Stack.Screen name="AdminCallQueue" component={AdminCallQueueScreen} />
        <Stack.Screen name="AdminCallDetails" component={AdminCallDetailsScreen} />
        <Stack.Screen name="AdminIssues" component={AdminIssuesScreen} />
        <Stack.Screen name="AdminIssueDetails" component={AdminIssueDetailsScreen} />
      </Stack.Group>

      {/* Citizen screens */}
      <Stack.Group>
        <Stack.Screen name="Main" component={BottomTabNavigator} />
        <Stack.Screen name="CallHistory" component={CallHistoryScreen} />
        <Stack.Screen name="Profile" component={ProfileScreen} />
        <Stack.Screen name="Utilities" component={UtilitiesScreen} />
        <Stack.Screen name="PaymentDetails" component={PaymentDetailsScreen} />
        <Stack.Screen name="PaymentConfirmation" component={PaymentConfirmationScreen} />
      </Stack.Group>

      {/* Health Services screens */}
      <Stack.Group>
        <Stack.Screen name="HealthServices" component={HealthServicesScreen} />
        <Stack.Screen name="BookingScreen" component={BookingScreen} />
        <Stack.Screen name="PrescriptionRefill" component={PrescriptionRefillScreen} />
        <Stack.Screen name="NotificationsScreen" component={NotificationsScreen} />
        <Stack.Screen name="ClinicLocatorScreen" component={ClinicLocatorScreen} />
        <Stack.Screen name="MedicalRecordsScreen" component={MedicalRecordsScreen} />
      </Stack.Group>

      {/* RDP Housing screens */}
      <Stack.Group>
        <Stack.Screen name="RDPHousing" component={RDPHousingScreen} />
        <Stack.Screen name="RDPApplication" component={RDPApplicationScreen} />
        <Stack.Screen name="RDPStatus" component={RDPStatusScreen} />
      </Stack.Group>

      {/* Doctor admin screens */}
      <Stack.Group>
        <Stack.Screen name="DoctorDashboard" component={DoctorDashboardScreen} />
        <Stack.Screen name="DoctorAppointments" component={DoctorAppointmentsScreen} />
        <Stack.Screen name="DoctorPatientDetails" component={DoctorPatientDetailsScreen} />
        <Stack.Screen name="DoctorPrescription" component={DoctorPrescriptionScreen} />
        <Stack.Screen name="DoctorReports" component={DoctorReportsScreen} />
      </Stack.Group>
    </Stack.Navigator>
  );
};

// Root component with providers
const App = () => {
  return (
    <SafeAreaProvider>
      <MockAuthProvider>
        <NavigationContainer
          // Add these options to help with react-native-screens issues
          documentTitle={{
            formatter: (options, route) =>
              `${options?.title ?? route?.name} - Let's Talk App`,
          }}
          theme={{
            dark: false,
            colors: {
              primary: COLORS.primaryGreen,
              background: COLORS.white,
              card: COLORS.white,
              text: COLORS.darkGray,
              border: COLORS.lightGray,
              notification: COLORS.primaryGreen,
            },
            // Add font configuration to prevent "regular of undefined" error
            fonts: {
              regular: {
                fontFamily: 'System',
                fontWeight: '400',
              },
              medium: {
                fontFamily: 'System',
                fontWeight: '500',
              },
              light: {
                fontFamily: 'System',
                fontWeight: '300',
              },
              thin: {
                fontFamily: 'System',
                fontWeight: '100',
              },
            },
          }}
        >
          <StatusBar
            backgroundColor={COLORS.primaryGreen}
            barStyle="light-content"
            translucent={true}
          />
          <AppNavigator />
        </NavigationContainer>
      </MockAuthProvider>
    </SafeAreaProvider>
  );
};



export default App;