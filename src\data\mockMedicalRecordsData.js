export const mockMedicalRecords = [
  {
    id: 'rec1',
    type: 'Consultation',
    date: '2023-10-26',
    doctor: 'Dr. <PERSON>',
    summary: 'Routine check-up. Patient reported feeling well. No significant findings. Advised on maintaining healthy diet and exercise.',
    details: 'Blood pressure: 120/80 mmHg, Heart rate: 72 bpm, Temperature: 36.8°C. Patient history reviewed. Discussed flu shot recommendation.',
  },
  {
    id: 'rec2',
    type: 'Prescription',
    date: '2023-09-15',
    doctor: 'Dr. <PERSON>',
    medication: 'Amoxicillin',
    dosage: '500mg',
    frequency: 'Three times a day',
    duration: '7 days',
    notes: 'Prescribed for mild throat infection.',
  },
  {
    id: 'rec3',
    type: 'Lab Result',
    date: '2023-08-01',
    test: 'Complete Blood Count (CBC)',
    result: 'Within normal limits.',
    details: 'WBC: 7.5 x 10^9/L, RBC: 4.8 x 10^12/L, Hemoglobin: 145 g/L, Platelets: 250 x 10^9/L.',
  },
  {
    id: 'rec4',
    type: 'Consultation',
    date: '2023-06-10',
    doctor: 'Dr. <PERSON>',
    summary: 'Follow-up for minor injury. Wound healing well. Advised to continue keeping the area clean and dry.',
    details: 'Patient presented with a small cut on the hand. Cleaned and dressed the wound. Provided instructions for home care.',
  },
  {
    id: 'rec5',
    type: 'Immunization',
    date: '2023-05-20',
    vaccine: 'Influenza Vaccine',
    notes: 'Annual flu shot administered.',
  },
];