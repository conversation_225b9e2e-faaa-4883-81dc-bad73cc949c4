export const mockMedicalRecords = [
  {
    id: 'rec1',
    type: 'Consultation',
    date: '2024-01-15',
    doctor: 'Dr. <PERSON><PERSON><PERSON>',
    clinic: 'Central City Clinic',
    summary: 'Annual health screening. Patient reports good general health. Blood pressure slightly elevated.',
    details: 'Blood pressure: 140/85 mmHg, Heart rate: 78 bpm, Temperature: 36.6°C, Weight: 72kg, Height: 165cm. Recommended lifestyle changes and follow-up in 3 months.',
    diagnosis: 'Pre-hypertension',
    recommendations: 'Reduce salt intake, increase physical activity, monitor blood pressure weekly',
  },
  {
    id: 'rec2',
    type: 'Prescription',
    date: '2024-01-15',
    doctor: 'Dr. <PERSON><PERSON><PERSON>hem<PERSON>',
    clinic: 'Central City Clinic',
    medication: 'Amlodipine',
    dosage: '5mg',
    frequency: 'Once daily',
    duration: '3 months',
    notes: 'Prescribed for blood pressure management. Take in the morning with food.',
    refills: 2,
  },
  {
    id: 'rec3',
    type: 'Lab Result',
    date: '2023-12-20',
    test: 'HIV Test',
    result: 'Negative',
    details: 'Rapid HIV test performed. Result: Non-reactive. Counseling provided on prevention methods.',
    clinic: 'Community Health Center',
    nextTest: '2024-12-20',
  },
  {
    id: 'rec4',
    type: 'Lab Result',
    date: '2023-12-20',
    test: 'TB Screening',
    result: 'Negative',
    details: 'Chest X-ray: Clear. Sputum test: Negative. No signs of tuberculosis.',
    clinic: 'Community Health Center',
    nextScreening: '2024-12-20',
  },
  {
    id: 'rec5',
    type: 'Immunization',
    date: '2023-11-10',
    vaccine: 'COVID-19 Booster',
    batchNumber: 'CV2023-SA-1145',
    clinic: 'Sunshine Children\'s Clinic',
    notes: 'Third dose administered. No adverse reactions observed. Next booster due in 12 months.',
    nextDue: '2024-11-10',
  },
  {
    id: 'rec6',
    type: 'Consultation',
    date: '2023-10-05',
    doctor: 'Dr. Thabo Molefe',
    clinic: 'Maternal Care Center',
    summary: 'Family planning consultation. Discussed contraceptive options.',
    details: 'Patient counseled on various contraceptive methods. Chose injectable contraceptive.',
    treatment: 'Depo-Provera injection administered',
    nextAppointment: '2024-01-05',
  },
  {
    id: 'rec7',
    type: 'Lab Result',
    date: '2023-09-15',
    test: 'Pap Smear',
    result: 'Normal',
    details: 'Cervical cytology: Normal epithelial cells. No abnormalities detected.',
    clinic: 'Maternal Care Center',
    nextTest: '2026-09-15',
  },
  {
    id: 'rec8',
    type: 'Prescription',
    date: '2023-08-22',
    doctor: 'Dr. Sarah Ndlovu',
    clinic: 'Central City Clinic',
    medication: 'Iron Supplements',
    dosage: '200mg',
    frequency: 'Twice daily',
    duration: '2 months',
    notes: 'Prescribed for iron deficiency anemia. Take with vitamin C for better absorption.',
    completed: true,
  },
  {
    id: 'rec9',
    type: 'Consultation',
    date: '2023-07-18',
    doctor: 'Dr. Michael van der Merwe',
    clinic: 'Bright Smile Dental Clinic',
    summary: 'Routine dental check-up and cleaning.',
    details: 'Oral examination: Good oral hygiene. One small cavity detected on upper left molar.',
    treatment: 'Dental cleaning performed. Filling scheduled for next visit.',
    nextAppointment: '2023-08-01',
  },
  {
    id: 'rec10',
    type: 'Immunization',
    date: '2023-06-12',
    vaccine: 'Tetanus Booster',
    batchNumber: 'TET2023-SA-0892',
    clinic: 'Central City Clinic',
    notes: 'Routine tetanus booster. Valid for 10 years.',
    nextDue: '2033-06-12',
  },
];