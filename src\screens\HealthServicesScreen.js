// HealthServicesScreen.js

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Alert,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Linking } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import CustomButton from '../components/CustomButton';
import {
  HEALTH_SERVICE_CATEGORIES,
  HEALTH_SERVICES,
  initializeMockHealthData,
} from '../data/mockHealthData';

const HealthServicesScreen = ({ navigation }) => {
  const [selectedCategory, setSelectedCategory] = useState('appointments');
  const [isLoading, setIsLoading] = useState(true);
  const scaleAnim = new Animated.Value(1);

  useEffect(() => {
    const setupData = async () => {
      await initializeMockHealthData();
      setIsLoading(false);
    };
    setupData();
  }, []);

  const filteredServices =
    selectedCategory === 'all'
      ? HEALTH_SERVICES
      : HEALTH_SERVICES.filter(
          (service) => service.category === selectedCategory
        );

  const handleServicePress = (service) => {
    if (service.screen) {
      navigation.navigate(service.screen);
    } else {
      Alert.alert('Coming Soon', 'This feature is coming soon!');
    }
  };

  const animateCategory = () => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.05,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const renderCategoryItem = ({ item }) => (
    <TouchableOpacity
      onPress={() => {
        setSelectedCategory(item.id);
        animateCategory();
      }}
    >
      <Animated.View
        style={[
          styles.categoryItem,
          selectedCategory === item.id && styles.selectedCategoryItem,
          { transform: [{ scale: selectedCategory === item.id ? scaleAnim : 1 }] },
        ]}
      >
        <Text
          style={[
            styles.categoryIcon,
            selectedCategory === item.id && styles.selectedCategoryIcon,
          ]}
        >
          {item.icon}
        </Text>
        <Text
          style={[
            styles.categoryName,
            selectedCategory === item.id && styles.selectedCategoryName,
          ]}
        >
          {item.name}
        </Text>
      </Animated.View>
    </TouchableOpacity>
  );

  const renderServiceItem = ({ item }) => (
    <TouchableOpacity
      style={styles.serviceCard}
      onPress={() => handleServicePress(item)}
    >
      <View style={styles.serviceIconContainer}>
        <Text style={styles.serviceIcon}>{item.icon}</Text>
      </View>
      <View style={styles.serviceContent}>
        <Text style={styles.serviceTitle}>{item.title}</Text>
        <Text style={styles.serviceDescription}>{item.description}</Text>
      </View>
      <Icon name="chevron-right" size={24} color={COLORS.primaryGreen} />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Health Services</Text>
      </View>

      <ScrollView
        contentContainerStyle={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Banner */}
        <View style={styles.bannerContainer}>
          <View style={styles.bannerContent}>
            <Text style={styles.bannerTitle}>Healthcare at your fingertips</Text>
            <Text style={styles.bannerSubtitle}>
              Book appointments, manage prescriptions, and more
            </Text>
          </View>
          <View style={styles.bannerIconContainer}>
            <Text style={styles.bannerIcon}>🏥</Text>
          </View>
        </View>

        {/* Categories */}
        <FlatList
          data={HEALTH_SERVICE_CATEGORIES}
          renderItem={renderCategoryItem}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesList}
        />

        {/* Search */}
        <TouchableOpacity
          style={styles.searchBar}
          onPress={() =>
            Alert.alert('Search', 'Search functionality coming soon!')
          }
        >
          <Text style={styles.searchPlaceholder}>
            🔍 Search for health services...
          </Text>
        </TouchableOpacity>

        {/* Services */}
        <View style={styles.servicesContainer}>
          <Text style={styles.sectionTitle}>
            {
              HEALTH_SERVICE_CATEGORIES.find(
                (cat) => cat.id === selectedCategory
              )?.name || 'All Services'
            }
          </Text>
          <FlatList
            data={filteredServices}
            renderItem={renderServiceItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            contentContainerStyle={styles.servicesList}
          />
        </View>

        {/* Emergency */}
        <View style={styles.emergencyContainer}>
          <Icon name="alert-circle" size={30} color={COLORS.white} />
          <Text style={styles.emergencyTitle}>Emergency?</Text>
          <Text style={styles.emergencyText}>
            In case of a medical emergency, call the national emergency number
            immediately.
          </Text>
          <CustomButton
            title="Call Emergency Services"
            type="secondary"
            onPress={() => {
              const phoneNumber = 'tel:112'; // Using 112 as a common emergency number
              Linking.canOpenURL(phoneNumber)
                .then((supported) => {
                  if (!supported) {
                    Alert.alert('Error', 'Phone calls are not supported on this device.');
                  } else {
                    return Linking.openURL(phoneNumber);
                  }
                })
                .catch((err) => console.error('An error occurred', err));
            }}
            style={styles.emergencyButton}
            textStyle={styles.emergencyButtonText}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    height: 120,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingTop: SPACING.lg,
  },
  backButton: {
    marginRight: SPACING.sm,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  scrollView: {
    paddingBottom: SPACING.xxl,
  },
  bannerContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.primaryGreen,
    borderRadius: 15,
    padding: SPACING.md,
    margin: SPACING.md,
    alignItems: 'center',
  },
  bannerContent: {
    flex: 3,
  },
  bannerTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
    marginBottom: SPACING.xs,
  },
  bannerSubtitle: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.white,
  },
  bannerIconContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bannerIcon: {
    fontSize: 40,
  },
  categoriesList: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
  },
  categoryItem: {
    alignItems: 'center',
    marginRight: SPACING.md,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: 20,
    backgroundColor: COLORS.offWhite,
    borderWidth: 1,
    borderColor: COLORS.primaryGreen,
  },
  selectedCategoryItem: {
    backgroundColor: COLORS.primaryGreen,
  },
  categoryIcon: {
    fontSize: FONT_SIZES.xl,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.xs,
  },
  selectedCategoryIcon: {
    color: COLORS.white,
  },
  categoryName: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
  },
  selectedCategoryName: {
    color: COLORS.white,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  searchBar: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 10,
    padding: SPACING.md,
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.primaryGreen,
  },
  searchPlaceholder: {
    color: COLORS.darkGray,
    fontSize: FONT_SIZES.md,
  },
  servicesContainer: {
    paddingHorizontal: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: SPACING.md,
  },
  servicesList: {
    paddingBottom: SPACING.sm,
  },
  serviceCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: SPACING.md,
    marginBottom: SPACING.sm,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
  },
    serviceIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.primaryLightGreen,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  serviceIcon: {
    fontSize: FONT_SIZES.xl,
    color: COLORS.white,
  },
  serviceContent: {
    flex: 1,
  },
  serviceTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
    marginBottom: 2,
  },
  serviceDescription: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  emergencyContainer: {
    backgroundColor: COLORS.primaryGreen,
    borderRadius: 15,
    padding: SPACING.md,
    margin: SPACING.md,
    marginTop: SPACING.xl,
    alignItems: 'center',
  },
  emergencyTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
    marginTop: SPACING.sm,
  },
  emergencyText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.white,
    textAlign: 'center',
    marginVertical: SPACING.sm,
  },
  emergencyButton: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    width: '100%',
    marginTop: SPACING.sm,
  },
  emergencyButtonText: {
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.bold,
    textAlign: 'center',
  },
});

export default HealthServicesScreen;
