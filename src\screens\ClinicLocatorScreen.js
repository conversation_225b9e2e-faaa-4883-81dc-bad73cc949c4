import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  Image,
  Linking,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import MapView, { Mark<PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import { PUBLIC_CLINICS } from '../data/mockHealthData';
import CustomButton from '../components/CustomButton';

const ClinicLocatorScreen = ({ navigation }) => {
  const [clinics, setClinics] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedClinic, setSelectedClinic] = useState(null);
  const [mapRegion, setMapRegion] = useState({
    latitude: -26.2041,
    longitude: 28.0473,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  });

  // Load clinics on component mount
  useEffect(() => {
    // Simulate loading from API
    setTimeout(() => {
      setClinics(PUBLIC_CLINICS);
      setLoading(false);
    }, 1000);
  }, []);

  // Handle clinic selection
  const handleClinicSelect = (clinic) => {
    setSelectedClinic(clinic);
    // Center map on selected clinic
    setMapRegion({
      latitude: clinic.latitude,
      longitude: clinic.longitude,
      latitudeDelta: 0.02,
      longitudeDelta: 0.02,
    });
  };

  // Handle get directions
  const handleGetDirections = (clinic) => {
    const scheme = Platform.select({ ios: 'maps:0,0?q=', android: 'geo:0,0?q=' });
    const latLng = `${clinic.latitude},${clinic.longitude}`;
    const label = clinic.name;
    const url = Platform.select({
      ios: `${scheme}${label}@${latLng}`,
      android: `${scheme}${latLng}(${label})`
    });

    Linking.openURL(url).catch(() => {
      Alert.alert('Error', 'Unable to open maps application');
    });
  };

  // Handle call clinic
  const handleCallClinic = (clinic) => {
    const phoneNumber = `tel:${clinic.phone}`;
    Linking.canOpenURL(phoneNumber)
      .then((supported) => {
        if (!supported) {
          Alert.alert('Error', 'Phone calls are not supported on this device.');
        } else {
          return Linking.openURL(phoneNumber);
        }
      })
      .catch((err) => console.error('An error occurred', err));
  };

  // Render clinic item
  const renderClinicItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.clinicItem,
        selectedClinic?.id === item.id && styles.selectedClinicItem
      ]}
      onPress={() => handleClinicSelect(item)}
    >
      <View style={styles.clinicHeader}>
        <View>
          <Text style={styles.clinicName}>{item.name}</Text>
          <Text style={styles.clinicDistance}>{item.distance} away</Text>
        </View>
        <View style={styles.clinicHoursContainer}>
          <Icon name="clock-outline" size={16} color={COLORS.primaryGreen} />
          <Text style={styles.clinicHours}>{item.hours}</Text>
        </View>
      </View>
      
      <Text style={styles.clinicAddress}>{item.address}</Text>
      
      <View style={styles.clinicServicesContainer}>
        <Text style={styles.clinicServicesTitle}>Services:</Text>
        <View style={styles.clinicServicesTags}>
          {item.services.map((service, index) => (
            <View key={index} style={styles.serviceTag}>
              <Text style={styles.serviceTagText}>{service}</Text>
            </View>
          ))}
        </View>
      </View>
      
      <View style={styles.clinicActions}>
        <TouchableOpacity
          style={styles.clinicActionButton}
          onPress={() => handleCallClinic(item)}
        >
          <Icon name="phone" size={20} color={COLORS.primaryGreen} />
          <Text style={styles.clinicActionText}>Call</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.clinicActionButton}
          onPress={() => handleGetDirections(item)}
        >
          <Icon name="map-marker" size={20} color={COLORS.primaryGreen} />
          <Text style={styles.clinicActionText}>Directions</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.black} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Public Clinics Near You</Text>
      </View>

      {/* Map View */}
      <View style={styles.mapContainer}>
        <MapView
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          region={mapRegion}
          onRegionChangeComplete={setMapRegion}
        >
          {clinics.map((clinic) => (
            <Marker
              key={clinic.id}
              coordinate={{
                latitude: clinic.latitude,
                longitude: clinic.longitude,
              }}
              title={clinic.name}
              description={clinic.address}
              onPress={() => handleClinicSelect(clinic)}
            />
          ))}
        </MapView>
      </View>

      {/* Clinics List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primaryGreen} />
          <Text style={styles.loadingText}>Finding clinics near you...</Text>
        </View>
      ) : (
        <View style={styles.clinicsContainer}>
          <Text style={styles.clinicsTitle}>
            {clinics.length} Public Clinics Found
          </Text>
          <FlatList
            data={clinics}
            renderItem={renderClinicItem}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.clinicsList}
            showsVerticalScrollIndicator={false}
          />
        </View>
      )}

      {/* Selected Clinic Details */}
      {selectedClinic && (
        <View style={styles.selectedClinicContainer}>
          <View style={styles.selectedClinicHeader}>
            <Text style={styles.selectedClinicName}>{selectedClinic.name}</Text>
            <TouchableOpacity onPress={() => setSelectedClinic(null)}>
              <Icon name="close" size={24} color={COLORS.black} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.selectedClinicDetails}>
            <View style={styles.selectedClinicDetail}>
              <Icon name="map-marker" size={20} color={COLORS.primaryGreen} />
              <Text style={styles.selectedClinicDetailText}>{selectedClinic.address}</Text>
            </View>
            
            <View style={styles.selectedClinicDetail}>
              <Icon name="phone" size={20} color={COLORS.primaryGreen} />
              <Text style={styles.selectedClinicDetailText}>{selectedClinic.phone}</Text>
            </View>
            
            <View style={styles.selectedClinicDetail}>
              <Icon name="clock-outline" size={20} color={COLORS.primaryGreen} />
              <Text style={styles.selectedClinicDetailText}>{selectedClinic.hours}</Text>
            </View>
          </View>
          
          <View style={styles.selectedClinicActions}>
            <CustomButton
              title="Call Clinic"
              type="outline"
              onPress={() => handleCallClinic(selectedClinic)}
              style={styles.selectedClinicActionButton}
            />
            <CustomButton
              title="Get Directions"
              onPress={() => handleGetDirections(selectedClinic)}
              style={styles.selectedClinicActionButton}
            />
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  backButton: {
    marginRight: SPACING.sm,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  mapContainer: {
    height: 250,
    backgroundColor: COLORS.offWhite,
  },
  map: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginTop: SPACING.md,
  },
  clinicsContainer: {
    flex: 1,
    padding: SPACING.md,
  },
  clinicsTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: SPACING.md,
  },
  clinicsList: {
    paddingBottom: SPACING.xxl,
  },
  clinicItem: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    // Shadow for iOS
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    // Shadow for Android
    elevation: 2,
  },
  selectedClinicItem: {
    borderWidth: 2,
    borderColor: COLORS.primaryGreen,
  },
  clinicHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.xs,
  },
  clinicName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
    marginBottom: 2,
  },
  clinicDistance: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  clinicHoursContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clinicHours: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
    marginLeft: 4,
  },
  clinicAddress: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: SPACING.sm,
  },
  clinicServicesContainer: {
    marginBottom: SPACING.sm,
  },
  clinicServicesTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: COLORS.black,
    marginBottom: SPACING.xs,
  },
  clinicServicesTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  serviceTag: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 15,
    paddingVertical: 4,
    paddingHorizontal: 8,
    marginRight: 6,
    marginBottom: 6,
  },
  serviceTagText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.darkGray,
  },
  clinicActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: COLORS.lightGray,
    paddingTop: SPACING.sm,
  },
  clinicActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.lg,
  },
  clinicActionText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.semiBold,
    marginLeft: 4,
  },
  selectedClinicContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SPACING.md,
    // Shadow for iOS
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    // Shadow for Android
    elevation: 5,
  },
  selectedClinicHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  selectedClinicName: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.black,
  },
  selectedClinicDetails: {
    marginBottom: SPACING.md,
  },
  selectedClinicDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  selectedClinicDetailText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
    marginLeft: SPACING.sm,
  },
  selectedClinicActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  selectedClinicActionButton: {
    flex: 1,
    marginHorizontal: SPACING.xs,
  },
});

export default ClinicLocatorScreen;
