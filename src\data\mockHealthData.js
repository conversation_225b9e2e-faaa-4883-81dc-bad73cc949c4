/**
 * Mock data for Health Services feature
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock healthcare providers
export const HEALTHCARE_PROVIDERS = [
  {
    id: 'provider-1',
    name: 'Dr. <PERSON>',
    specialty: 'General Practitioner',
    location: 'Central City Clinic',
    availability: ['Monday', 'Wednesday', 'Friday'],
  },
  {
    id: 'provider-2',
    name: 'Dr. <PERSON>',
    specialty: 'Pediatrician',
    location: 'Sunshine Children\'s Clinic',
    availability: ['Tuesday', 'Thursday', 'Saturday'],
  },
  {
    id: 'provider-3',
    name: 'Dr. <PERSON><PERSON><PERSON>',
    specialty: 'Dentist',
    location: 'Bright Smile Dental Clinic',
    availability: ['Monday', 'Tuesday', 'Thursday'],
  },
  {
    id: 'provider-4',
    name: 'Nurse <PERSON><PERSON>',
    specialty: 'Vaccination Nurse',
    location: 'Community Health Center',
    availability: ['Monday', 'Wednesday', 'Friday'],
  },
  {
    id: 'provider-5',
    name: 'Dr. <PERSON>',
    specialty: 'Obstetrician',
    location: 'Maternal Care Center',
    availability: ['Tuesday', 'Thursday', 'Friday'],
  },
];

// Mock public clinics
export const PUBLIC_CLINICS = [
  {
    id: 'clinic-1',
    name: 'Central City Clinic',
    address: '123 Main Street, Central City',
    phone: '************',
    hours: '8:00 AM - 5:00 PM',
    services: ['General Consultations', 'Vaccinations', 'Maternal Care', 'HIV Testing'],
    distance: '1.2 km',
  },
  {
    id: 'clinic-2',
    name: 'Sunshine Children\'s Clinic',
    address: '45 Park Avenue, Westside',
    phone: '************',
    hours: '8:30 AM - 4:30 PM',
    services: ['Pediatric Care', 'Vaccinations', 'Growth Monitoring'],
    distance: '3.5 km',
  },
  {
    id: 'clinic-3',
    name: 'Community Health Center',
    address: '78 Freedom Road, Eastside',
    phone: '************',
    hours: '8:00 AM - 6:00 PM',
    services: ['General Consultations', 'Chronic Medication', 'TB Treatment', 'Family Planning'],
    distance: '2.8 km',
  },
  {
    id: 'clinic-4',
    name: 'Maternal Care Center',
    address: '15 Hope Street, Northside',
    phone: '************',
    hours: '8:00 AM - 4:00 PM',
    services: ['Antenatal Care', 'Postnatal Care', 'Family Planning'],
    distance: '4.1 km',
  },
  {
    id: 'clinic-5',
    name: 'Bright Smile Dental Clinic',
    address: '32 Smile Road, Southside',
    phone: '************',
    hours: '9:00 AM - 5:00 PM',
    services: ['Dental Check-ups', 'Extractions', 'Fillings'],
    distance: '3.2 km',
  },
];

// Mock health service categories
export const HEALTH_SERVICE_CATEGORIES = [
  { id: 'appointments', name: 'Appointments', icon: '📅' },
  { id: 'medication', name: 'Medication', icon: '💊' },
  { id: 'records', name: 'Medical Records', icon: '📋' },
  { id: 'clinics', name: 'Find Clinics', icon: '🏥' },
  { id: 'emergency', name: 'Emergency', icon: '🚑' },
];

// Mock health services
export const HEALTH_SERVICES = [
  {
    id: 'service-1',
    title: 'Book Appointment',
    description: 'Schedule a visit with a healthcare provider',
    icon: '📅',
    category: 'appointments',
    screen: 'BookingScreen',
  },
  {
    id: 'service-2',
    title: 'Medication Reminders',
    description: 'Set up reminders for your medication',
    icon: '⏰',
    category: 'medication',
    screen: 'NotificationsScreen',
  },
  {
    id: 'service-3',
    title: 'Prescription Refills',
    description: 'Request refills for your prescriptions',
    icon: '💊',
    category: 'medication',
    screen: 'PrescriptionRefill',
  },
  {
    id: 'service-4',
    title: 'Medical Records',
    description: 'Access your medical history and test results',
    icon: '📋',
    category: 'records',
    screen: 'MedicalRecordsScreen',
  },
  {
    id: 'service-5',
    title: 'Find Public Clinics',
    description: 'Locate public clinics near you',
    icon: '🏥',
    category: 'clinics',
    screen: 'ClinicLocatorScreen',
  },
  {
    id: 'service-6',
    title: 'Emergency Contacts',
    description: 'Quick access to emergency services',
    icon: '🚑',
    category: 'emergency',
    screen: 'NotificationsScreen',
  },
];

// Mock notifications
export const MOCK_NOTIFICATIONS = [
  {
    id: 'notif-1',
    title: 'Appointment Reminder',
    message: 'You have an appointment with Dr. Sarah Johnson tomorrow at 10:00 AM at Central City Clinic.',
    type: 'appointment',
    date: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
    read: false,
  },
  {
    id: 'notif-2',
    title: 'Medication Pickup',
    message: 'Your prescription is ready for collection at Central City Pharmacy.',
    type: 'medication',
    date: new Date().toISOString(), // Today
    read: false,
  },
  {
    id: 'notif-3',
    title: 'Test Results Available',
    message: 'Your recent blood test results are now available. Please log in to view them.',
    type: 'results',
    date: new Date(Date.now() - 86400000).toISOString(), // Yesterday
    read: true,
  },
  {
    id: 'notif-4',
    title: 'Vaccination Due',
    message: 'Your annual flu vaccination is due this month. Please schedule an appointment.',
    type: 'vaccination',
    date: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    read: true,
  },
];

// Initialize mock data in AsyncStorage
export const initializeMockHealthData = async () => {
  try {
    // Check if data already exists
    const appointmentsJson = await AsyncStorage.getItem('@health_appointments');
    const notificationsJson = await AsyncStorage.getItem('@health_notifications');
    
    // If not, initialize with mock data
    if (!appointmentsJson) {
      await AsyncStorage.setItem('@health_appointments', JSON.stringify([]));
    }
    
    if (!notificationsJson) {
      await AsyncStorage.setItem('@health_notifications', JSON.stringify(MOCK_NOTIFICATIONS));
    }
    
    return true;
  } catch (error) {
    console.error('Error initializing mock health data:', error);
    return false;
  }
};

// Get appointments
export const getAppointments = async () => {
  try {
    const appointmentsJson = await AsyncStorage.getItem('@health_appointments');
    return appointmentsJson ? JSON.parse(appointmentsJson) : [];
  } catch (error) {
    console.error('Error getting appointments:', error);
    return [];
  }
};

// Add a new appointment
export const addAppointment = async (appointmentData) => {
  try {
    const appointments = await getAppointments();
    const newAppointment = {
      id: `appointment-${Date.now()}`,
      timestamp: new Date().toISOString(),
      status: 'scheduled',
      ...appointmentData
    };

    const updatedAppointments = [...appointments, newAppointment];
    await AsyncStorage.setItem('@health_appointments', JSON.stringify(updatedAppointments));
    
    // Add a notification for the appointment
    const appointmentDate = new Date(appointmentData.date);
    const formattedDate = appointmentDate.toLocaleDateString();
    const notification = {
      id: `notif-appointment-${Date.now()}`,
      title: 'Appointment Scheduled',
      message: `Your appointment with ${appointmentData.provider} has been scheduled for ${formattedDate} at ${appointmentData.time}.`,
      type: 'appointment',
      date: new Date().toISOString(),
      read: false,
    };
    await addNotification(notification);
    
    return newAppointment;
  } catch (error) {
    console.error('Error adding appointment:', error);
    return null;
  }
};

// Get notifications
export const getNotifications = async () => {
  try {
    const notificationsJson = await AsyncStorage.getItem('@health_notifications');
    return notificationsJson ? JSON.parse(notificationsJson) : [];
  } catch (error) {
    console.error('Error getting notifications:', error);
    return [];
  }
};

// Add a new notification
export const addNotification = async (notificationData) => {
  try {
    const notifications = await getNotifications();
    const newNotification = {
      id: `notif-${Date.now()}`,
      timestamp: new Date().toISOString(),
      read: false,
      ...notificationData
    };

    const updatedNotifications = [...notifications, newNotification];
    await AsyncStorage.setItem('@health_notifications', JSON.stringify(updatedNotifications));
    return newNotification;
  } catch (error) {
    console.error('Error adding notification:', error);
    return null;
  }
};

// Mark notification as read
export const markNotificationAsRead = async (notificationId) => {
  try {
    const notifications = await getNotifications();
    const updatedNotifications = notifications.map(notification => 
      notification.id === notificationId 
        ? { ...notification, read: true } 
        : notification
    );
    
    await AsyncStorage.setItem('@health_notifications', JSON.stringify(updatedNotifications));
    return true;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return false;
  }
};

export default {
  HEALTHCARE_PROVIDERS,
  PUBLIC_CLINICS,
  HEALTH_SERVICE_CATEGORIES,
  HEALTH_SERVICES,
  MOCK_NOTIFICATIONS,
  initializeMockHealthData,
  getAppointments,
  addAppointment,
  getNotifications,
  addNotification,
  markNotificationAsRead,
};
