import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import { mockMedicalRecords } from '../data/mockMedicalRecordsData';

const MedicalRecordsScreen = ({ navigation }) => {
  const getRecordIcon = (type) => {
    switch (type) {
      case 'Consultation': return '👨‍⚕️';
      case 'Prescription': return '💊';
      case 'Lab Result': return '🧪';
      case 'Immunization': return '💉';
      default: return '📋';
    }
  };

  const getRecordColor = (type) => {
    switch (type) {
      case 'Consultation': return COLORS.primaryGreen;
      case 'Prescription': return COLORS.warning;
      case 'Lab Result': return COLORS.info;
      case 'Immunization': return COLORS.success;
      default: return COLORS.primaryGreen;
    }
  };

  const renderRecordItem = ({ item }) => (
    <View style={styles.recordCard}>
      <View style={styles.recordHeader}>
        <View style={styles.recordTypeContainer}>
          <Text style={styles.recordIcon}>{getRecordIcon(item.type)}</Text>
          <Text style={[styles.recordType, { color: getRecordColor(item.type) }]}>
            {item.type}
          </Text>
        </View>
        <Text style={styles.recordDate}>{item.date}</Text>
      </View>

      {item.clinic && (
        <View style={styles.recordInfoRow}>
          <Icon name="hospital-building" size={16} color={COLORS.darkGray} />
          <Text style={styles.recordInfo}>{item.clinic}</Text>
        </View>
      )}

      {item.doctor && (
        <View style={styles.recordInfoRow}>
          <Icon name="doctor" size={16} color={COLORS.darkGray} />
          <Text style={styles.recordInfo}>{item.doctor}</Text>
        </View>
      )}

      {item.summary && <Text style={styles.recordSummary}>{item.summary}</Text>}

      {/* Prescription Details */}
      {item.medication && (
        <View style={styles.medicationContainer}>
          <Text style={styles.medicationTitle}>💊 Medication Details:</Text>
          <Text style={styles.recordDetail}>• {item.medication} - {item.dosage}</Text>
          <Text style={styles.recordDetail}>• {item.frequency} for {item.duration}</Text>
          {item.refills && <Text style={styles.recordDetail}>• {item.refills} refills remaining</Text>}
        </View>
      )}

      {/* Lab Results */}
      {item.test && (
        <View style={styles.labContainer}>
          <Text style={styles.labTitle}>🧪 {item.test}</Text>
          <Text style={[styles.labResult, { color: item.result === 'Negative' || item.result === 'Normal' ? COLORS.success : COLORS.warning }]}>
            Result: {item.result}
          </Text>
          {item.nextTest && <Text style={styles.recordDetail}>Next test due: {item.nextTest}</Text>}
        </View>
      )}

      {/* Immunization */}
      {item.vaccine && (
        <View style={styles.vaccineContainer}>
          <Text style={styles.vaccineTitle}>💉 {item.vaccine}</Text>
          {item.batchNumber && <Text style={styles.recordDetail}>Batch: {item.batchNumber}</Text>}
          {item.nextDue && <Text style={styles.recordDetail}>Next due: {item.nextDue}</Text>}
        </View>
      )}

      {item.details && <Text style={styles.recordDetails}>{item.details}</Text>}
      {item.diagnosis && <Text style={styles.diagnosis}>Diagnosis: {item.diagnosis}</Text>}
      {item.recommendations && <Text style={styles.recommendations}>Recommendations: {item.recommendations}</Text>}
      {item.notes && <Text style={styles.recordNotes}>{item.notes}</Text>}

      {item.nextAppointment && (
        <View style={styles.nextAppointmentContainer}>
          <Icon name="calendar-clock" size={16} color={COLORS.primaryGreen} />
          <Text style={styles.nextAppointment}>Next appointment: {item.nextAppointment}</Text>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Medical Records</Text>
      </View>

      <FlatList
        data={mockMedicalRecords}
        renderItem={renderRecordItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No medical records found.</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    height: 120,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingTop: SPACING.lg,
  },
  backButton: {
    marginRight: SPACING.sm,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  listContainer: {
    padding: SPACING.md,
  },
  recordCard: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.primaryLightGreen,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
  },
  recordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  recordTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recordIcon: {
    fontSize: FONT_SIZES.lg,
    marginRight: SPACING.xs,
  },
  recordType: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
  },
  recordDate: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    fontWeight: FONT_WEIGHTS.semiBold,
  },
  recordInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  recordInfo: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginLeft: SPACING.xs,
  },
  recordSummary: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.text,
    marginBottom: SPACING.sm,
    lineHeight: 20,
  },
  medicationContainer: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 8,
    padding: SPACING.sm,
    marginVertical: SPACING.xs,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.warning,
  },
  medicationTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.warning,
    marginBottom: SPACING.xs,
  },
  labContainer: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 8,
    padding: SPACING.sm,
    marginVertical: SPACING.xs,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.info,
  },
  labTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.info,
    marginBottom: SPACING.xs,
  },
  labResult: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.bold,
    marginBottom: SPACING.xs,
  },
  vaccineContainer: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 8,
    padding: SPACING.sm,
    marginVertical: SPACING.xs,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.success,
  },
  vaccineTitle: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.success,
    marginBottom: SPACING.xs,
  },
  recordDetails: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.text,
    fontStyle: 'italic',
    marginTop: SPACING.xs,
    lineHeight: 18,
  },
  diagnosis: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.semiBold,
    marginTop: SPACING.xs,
  },
  recommendations: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.text,
    marginTop: SPACING.xs,
    lineHeight: 18,
  },
  recordDetail: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.text,
    marginTop: 2,
  },
  recordNotes: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    fontStyle: 'italic',
    marginTop: SPACING.xs,
  },
  nextAppointmentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: SPACING.sm,
    padding: SPACING.xs,
    backgroundColor: COLORS.primaryLightGreen,
    borderRadius: 6,
  },
  nextAppointment: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.primaryGreen,
    fontWeight: FONT_WEIGHTS.semiBold,
    marginLeft: SPACING.xs,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: SPACING.xxl,
  },
  emptyText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
});

export default MedicalRecordsScreen;