import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { COLORS, FONT_SIZES, SPACING, FONT_WEIGHTS } from '../theme/colors';
import { mockMedicalRecords } from '../data/mockMedicalRecordsData';

const MedicalRecordsScreen = ({ navigation }) => {
  const renderRecordItem = ({ item }) => (
    <View style={styles.recordCard}>
      <View style={styles.recordHeader}>
        <Text style={styles.recordType}>{item.type}</Text>
        <Text style={styles.recordDate}>{item.date}</Text>
      </View>
      {item.doctor && <Text style={styles.recordDoctor}>Doctor: {item.doctor}</Text>}
      {item.summary && <Text style={styles.recordSummary}>{item.summary}</Text>}
      {item.details && <Text style={styles.recordDetails}>Details: {item.details}</Text>}
      {item.medication && <Text style={styles.recordDetail}>Medication: {item.medication}</Text>}
      {item.dosage && <Text style={styles.recordDetail}>Dosage: {item.dosage}</Text>}
      {item.frequency && <Text style={styles.recordDetail}>Frequency: {item.frequency}</Text>}
      {item.duration && <Text style={styles.recordDetail}>Duration: {item.duration}</Text>}
      {item.test && <Text style={styles.recordDetail}>Test: {item.test}</Text>}
      {item.result && <Text style={styles.recordDetail}>Result: {item.result}</Text>}
      {item.vaccine && <Text style={styles.recordDetail}>Vaccine: {item.vaccine}</Text>}
      {item.notes && <Text style={styles.recordDetail}>Notes: {item.notes}</Text>}
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-left" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Medical Records</Text>
      </View>

      <FlatList
        data={mockMedicalRecords}
        renderItem={renderRecordItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No medical records found.</Text>
          </View>
        )}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  header: {
    height: 120,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    backgroundColor: COLORS.primaryGreen,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingTop: SPACING.lg,
  },
  backButton: {
    marginRight: SPACING.sm,
  },
  headerTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.white,
  },
  listContainer: {
    padding: SPACING.md,
  },
  recordCard: {
    backgroundColor: COLORS.offWhite,
    borderRadius: 10,
    padding: SPACING.md,
    marginBottom: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.primaryLightGreen,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 3,
  },
  recordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  recordType: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.bold,
    color: COLORS.primaryGreen,
  },
  recordDate: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
  },
  recordDoctor: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.darkGray,
    marginBottom: SPACING.xs,
  },
  recordSummary: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  recordDetails: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.text,
    fontStyle: 'italic',
    marginTop: SPACING.xs,
  },
  recordDetail: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.text,
    marginTop: SPACING.xs,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: SPACING.xxl,
  },
  emptyText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.darkGray,
  },
});

export default MedicalRecordsScreen;